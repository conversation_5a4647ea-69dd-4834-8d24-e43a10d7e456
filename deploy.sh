#!/bin/bash

# Скрипт для деплоя телеграм бота на Beget

echo "🚀 Начинаем деплой телеграм бота..."

# Функция установки Docker
install_docker() {
    echo "📦 Устанавливаем Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
    rm get-docker.sh
    echo "✅ Docker установлен"
}

# Функция установки Docker Compose
install_docker_compose() {
    echo "📦 Устанавливаем Docker Compose..."
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    echo "✅ Docker Compose установлен"
}

# Функция установки Git
install_git() {
    echo "📦 Устанавливаем Git..."
    sudo apt update
    sudo apt install -y git
    echo "✅ Git установлен"
}

# Функция установки Certbot
install_certbot() {
    echo "📦 Устанавливаем Certbot..."
    sudo apt update
    sudo apt install -y certbot
    echo "✅ Certbot установлен"
}

# Проверяем и устанавливаем зависимости
echo "🔍 Проверяем системные зависимости..."

# Проверяем Docker
if ! command -v docker &> /dev/null; then
    echo "⚠️ Docker не установлен"
    read -p "Установить Docker автоматически? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        install_docker
        echo "🔄 Перезайдите в систему для применения прав Docker"
        echo "Затем запустите скрипт снова"
        exit 0
    else
        echo "❌ Docker необходим для работы. Установите вручную и запустите скрипт снова."
        exit 1
    fi
fi

# Проверяем Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "⚠️ Docker Compose не установлен"
    read -p "Установить Docker Compose автоматически? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        install_docker_compose
    else
        echo "❌ Docker Compose необходим для работы. Установите вручную и запустите скрипт снова."
        exit 1
    fi
fi

# Проверяем Git
if ! command -v git &> /dev/null; then
    echo "⚠️ Git не установлен"
    read -p "Установить Git автоматически? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        install_git
    fi
fi

# Проверяем Certbot
if ! command -v certbot &> /dev/null; then
    echo "⚠️ Certbot не установлен (нужен для SSL)"
    read -p "Установить Certbot автоматически? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        install_certbot
    fi
fi

echo "✅ Все зависимости проверены"

# Проверяем наличие файла с переменными окружения
if [ ! -f /etc/telebot/env ]; then
    echo "❌ Файл /etc/telebot/env не найден."
    echo "📝 Запустите: chmod +x scripts/setup_env.sh && sudo ./scripts/setup_env.sh"
    echo "📝 Затем отредактируйте: sudo nano /etc/telebot/env"
    exit 1
fi

# Останавливаем существующие контейнеры
echo "🛑 Останавливаем существующие контейнеры..."
docker-compose down

# Удаляем старые образы
echo "🗑️ Удаляем старые образы..."
docker-compose down --rmi all

# Собираем новые образы
echo "🔨 Собираем новые образы..."
docker-compose build --no-cache

# Запускаем контейнеры
echo "▶️ Запускаем контейнеры..."
docker-compose up -d

# Проверяем статус
echo "📊 Проверяем статус контейнеров..."
docker-compose ps

# Ждем запуска контейнеров
echo "⏳ Ждем запуска контейнеров..."
sleep 10

# Проверяем что все контейнеры запущены
if docker-compose ps | grep -q "Up"; then
    echo "✅ Контейнеры запущены успешно"

    # Проверяем nginx
    if docker-compose ps nginx | grep -q "Up"; then
        echo "✅ Nginx запущен"
    else
        echo "⚠️ Nginx не запущен, проверьте конфигурацию"
    fi

    # Проверяем бота
    if docker-compose ps bot | grep -q "Up"; then
        echo "✅ Бот запущен"
    else
        echo "⚠️ Бот не запущен, проверьте логи"
    fi

    # Проверяем базу данных
    if docker-compose ps postgres | grep -q "Up"; then
        echo "✅ База данных запущена"
    else
        echo "⚠️ База данных не запущена, проверьте конфигурацию"
    fi
else
    echo "❌ Ошибка запуска контейнеров"
    docker-compose logs
    exit 1
fi

echo ""
echo "🎉 Деплой завершен!"
echo "📝 Для просмотра логов: docker-compose logs -f bot"
echo "🌐 Для проверки nginx: docker-compose logs -f nginx"
echo "🛑 Для остановки: docker-compose down"
