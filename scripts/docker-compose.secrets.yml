version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: telebot_postgres
    environment:
      POSTGRES_DB: telebot
      POSTGRES_USER: telebot_user
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
    secrets:
      - postgres_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - telebot_network

  nginx:
    image: nginx:alpine
    container_name: telebot_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      bot:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - telebot_network

  bot:
    build: .
    container_name: telebot_app
    environment:
      BOT_TOKEN_FILE: /run/secrets/bot_token
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
      POSTGRES_DB: telebot
      POSTGRES_USER: telebot_user
      ENVIRONMENT: production
      WEBHOOK_MODE: true
      WEBHOOK_HOST: https://your-domain.com
      WEBHOOK_PATH: /webhook
      WEB_SERVER_HOST: 0.0.0.0
      WEB_SERVER_PORT: 8000
    secrets:
      - bot_token
      - postgres_password
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - telebot_network
    volumes:
      - ./logs:/app/logs
    expose:
      - "8000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:

networks:
  telebot_network:
    driver: bridge

secrets:
  bot_token:
    file: /etc/telebot/secrets/bot_token.txt
  postgres_password:
    file: /etc/telebot/secrets/postgres_password.txt
